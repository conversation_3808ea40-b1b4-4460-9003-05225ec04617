version: "3.9"

services:
  pw1:
    build: .
    container_name: pw1
    init: true
    ipc: host
    environment:
      - DISPLAY=:99
      - SCREEN_RESOLUTION=1280x800x24
      - VNC_PORT=5901
      - NOVNC_PORT=6081
      - SERVER_PORT=3001
      - PW_VERSION=1.55.0
      - PWDEBUG=0
      - PLAYWRIGHT_HEADLESS=0
    ports:
      - "3001:3001"   # Playwright Server WS
      - "5901:5901"   # VNC (x11vnc)
      - "6081:6081"   # noVNC (web)
    shm_size: "2gb"
    restart: unless-stopped

  pw2:
    build: .
    container_name: pw2
    init: true
    ipc: host
    environment:
      - DISPLAY=:99
      - SCREEN_RESOLUTION=1280x800x24
      - VNC_PORT=5902
      - NOVNC_PORT=6082
      - SERVER_PORT=3002
      - PW_VERSION=1.55.0
      - PWDEBUG=1
      - PLAYWRIGHT_HEADLESS=0
    ports:
      - "3002:3002"
      - "5902:5902"
      - "6082:6082"
    shm_size: "2gb"
    restart: unless-stopped
